import express from 'express';
import cors from 'cors';
// import dotenv from 'dotenv';
import {
  Connection,
  PublicKey,
  clusterApiUrl,
  Transaction,
  SystemProgram,
  LAMPORTS_PER_SOL
} from '@solana/web3.js';

// Load environment variables
// dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;
const CORS_ORIGIN = process.env.CORS_ORIGIN || 'http://localhost:3000';
const SOLANA_NETWORK = process.env.SOLANA_NETWORK || 'devnet';
const SOLANA_RPC_URL = process.env.SOLANA_RPC_URL || clusterApiUrl(SOLANA_NETWORK);
const DONATION_WALLET = process.env.DONATION_WALLET;

// Solana connection
const connection = new Connection(SOLANA_RPC_URL, 'confirmed');

// Middleware
app.use(cors({
  origin: [CORS_ORIGIN, 'http://127.0.0.1:3000'],
  credentials: true
}));

app.use(express.json());

// Basic health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'NukeIsreal Backend API is running',
    network: SOLANA_NETWORK,
    rpcUrl: SOLANA_RPC_URL,
    environment: process.env.NODE_ENV || 'development',
    timestamp: new Date().toISOString()
  });
});

// Wallet validation endpoint
app.post('/api/validate-wallet', async (req, res) => {
  try {
    const { wallet } = req.body;

    // Validate request body
    if (!wallet) {
      return res.status(400).json({
        success: false,
        error: 'Wallet address is required'
      });
    }

    // Validate wallet address format
    try {
      const publicKey = new PublicKey(wallet);

      // Check if the public key is valid (on curve)
      if (!PublicKey.isOnCurve(publicKey)) {
        throw new Error('Invalid public key');
      }

      console.log(`Wallet validation request for: ${wallet}`);

      // For now, we'll just validate the format and return success
      // Later, we can add signature verification here

      res.json({
        success: true,
        message: 'Wallet validated successfully',
        wallet: wallet,
        network: 'devnet'
      });

    } catch (error) {
      console.error('Invalid wallet address:', error.message);
      return res.status(400).json({
        success: false,
        error: 'Invalid wallet address format'
      });
    }

  } catch (error) {
    console.error('Error validating wallet:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

// Donation processing endpoint
app.post('/api/donate', async (req, res) => {
  try {
    const {
      senderPublicKey,
      amount,
      operationId,
      operationTitle
    } = req.body;

    // Validate request body
    if (!senderPublicKey || !amount || !operationId) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: senderPublicKey, amount, operationId',
        code: 'INVALID_REQUEST'
      });
    }

    // Validate donation wallet is configured
    if (!DONATION_WALLET) {
      console.error('DONATION_WALLET not configured in environment variables');
      return res.status(500).json({
        success: false,
        error: 'Donation wallet not configured',
        code: 'CONFIG_ERROR'
      });
    }

    // Validate amount
    const donationAmount = parseFloat(amount);
    if (isNaN(donationAmount) || donationAmount <= 0) {
      return res.status(400).json({
        success: false,
        error: 'Invalid donation amount',
        code: 'INVALID_AMOUNT'
      });
    }

    if (donationAmount < 0.001) {
      return res.status(400).json({
        success: false,
        error: 'Minimum donation amount is 0.001 SOL',
        code: 'AMOUNT_TOO_LOW'
      });
    }

    if (donationAmount > 1000) {
      return res.status(400).json({
        success: false,
        error: 'Maximum donation amount is 1000 SOL',
        code: 'AMOUNT_TOO_HIGH'
      });
    }

    // Validate public keys
    let fromPubkey, toPubkey;
    try {
      fromPubkey = new PublicKey(senderPublicKey);
      toPubkey = new PublicKey(DONATION_WALLET);

      if (!PublicKey.isOnCurve(fromPubkey) || !PublicKey.isOnCurve(toPubkey)) {
        throw new Error('Invalid public key');
      }
    } catch (error) {
      return res.status(400).json({
        success: false,
        error: 'Invalid wallet address format',
        code: 'INVALID_WALLET'
      });
    }

    // Check sender balance
    const balance = await connection.getBalance(fromPubkey);
    const balanceSOL = balance / LAMPORTS_PER_SOL;

    if (balanceSOL < donationAmount) {
      return res.status(400).json({
        success: false,
        error: `Insufficient balance. You have ${balanceSOL.toFixed(4)} SOL, but need ${donationAmount} SOL`,
        code: 'INSUFFICIENT_BALANCE'
      });
    }

    // Create transaction
    const lamports = Math.round(donationAmount * LAMPORTS_PER_SOL);
    const transaction = new Transaction().add(
      SystemProgram.transfer({
        fromPubkey,
        toPubkey,
        lamports,
      })
    );

    // Get recent blockhash
    const { blockhash } = await connection.getLatestBlockhash();
    transaction.recentBlockhash = blockhash;
    transaction.feePayer = fromPubkey;

    // Serialize transaction for frontend to sign
    const serializedTransaction = transaction.serialize({
      requireAllSignatures: false,
      verifySignatures: false,
    });

    // Log the donation attempt
    const timestamp = new Date().toISOString();
    console.log(`🎖️ DONATION OPERATION INITIATED:`, {
      timestamp,
      operation: operationTitle || `Operation ${operationId}`,
      amount: `${donationAmount} SOL`,
      sender: senderPublicKey,
      recipient: DONATION_WALLET,
      network: SOLANA_NETWORK.toUpperCase()
    });

    // Return transaction for frontend to sign and submit
    res.json({
      success: true,
      message: 'Transaction prepared for deployment',
      data: {
        transaction: Buffer.from(serializedTransaction).toString('base64'),
        amount: donationAmount,
        recipient: DONATION_WALLET,
        sender: senderPublicKey,
        operation: operationTitle || `Operation ${operationId}`,
        network: SOLANA_NETWORK,
        timestamp
      },
      code: 'TRANSACTION_READY'
    });

  } catch (error) {
    console.error('🚨 DONATION OPERATION FAILED:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during donation processing',
      code: 'PROCESSING_ERROR'
    });
  }
});

// Transaction confirmation endpoint
app.post('/api/confirm-donation', async (req, res) => {
  try {
    const { signature, amount, operationId, operationTitle } = req.body;

    if (!signature) {
      return res.status(400).json({
        success: false,
        error: 'Transaction signature required',
        code: 'MISSING_SIGNATURE'
      });
    }

    // Wait for confirmation
    const confirmation = await connection.confirmTransaction(signature, 'confirmed');

    if (confirmation.value.err) {
      console.error('🚨 TRANSACTION FAILED:', confirmation.value.err);
      return res.status(400).json({
        success: false,
        error: 'Transaction failed on blockchain',
        details: confirmation.value.err,
        code: 'TRANSACTION_FAILED'
      });
    }

    // Log successful donation
    const timestamp = new Date().toISOString();
    console.log(`🎉 MISSION ACCOMPLISHED:`, {
      timestamp,
      operation: operationTitle || `Operation ${operationId}`,
      amount: `${amount} SOL`,
      signature,
      status: 'CONFIRMED',
      network: SOLANA_NETWORK.toUpperCase()
    });

    res.json({
      success: true,
      message: 'Mission accomplished! Donation confirmed on blockchain',
      data: {
        signature,
        amount,
        operation: operationTitle || `Operation ${operationId}`,
        status: 'CONFIRMED',
        network: SOLANA_NETWORK,
        timestamp,
        explorerUrl: `https://explorer.solana.com/tx/${signature}?cluster=${SOLANA_NETWORK}`
      },
      code: 'MISSION_ACCOMPLISHED'
    });

  } catch (error) {
    console.error('🚨 CONFIRMATION FAILED:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to confirm transaction',
      code: 'CONFIRMATION_ERROR'
    });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err);
  res.status(500).json({
    success: false,
    error: 'Internal server error'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 NukeIsreal Backend API running on port ${PORT}`);
  console.log(`📡 Connected to Solana ${SOLANA_NETWORK.toUpperCase()}`);
  console.log(`🌐 RPC URL: ${SOLANA_RPC_URL}`);
  console.log(`🔗 Health check: http://localhost:${PORT}/api/health`);
  console.log(`🔒 CORS Origin: ${CORS_ORIGIN}`);
});
