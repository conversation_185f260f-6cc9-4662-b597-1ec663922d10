import express from 'express';
import cors from 'cors';
// import dotenv from 'dotenv';
import { Connection, PublicKey, clusterApiUrl } from '@solana/web3.js';

// Load environment variables
// dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;
const CORS_ORIGIN = process.env.CORS_ORIGIN || 'http://localhost:3000';
const SOLANA_NETWORK = process.env.SOLANA_NETWORK || 'devnet';
const SOLANA_RPC_URL = process.env.SOLANA_RPC_URL || clusterApiUrl(SOLANA_NETWORK);

// Solana connection
const connection = new Connection(SOLANA_RPC_URL, 'confirmed');

// Middleware
app.use(cors({
  origin: [CORS_ORIGIN, 'http://127.0.0.1:3000'],
  credentials: true
}));

app.use(express.json());

// Basic health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'NukeIsreal Backend API is running',
    network: SOLANA_NETWORK,
    rpcUrl: SOLANA_RPC_URL,
    environment: process.env.NODE_ENV || 'development',
    timestamp: new Date().toISOString()
  });
});

// Wallet validation endpoint
app.post('/api/validate-wallet', async (req, res) => {
  try {
    const { wallet } = req.body;

    // Validate request body
    if (!wallet) {
      return res.status(400).json({
        success: false,
        error: 'Wallet address is required'
      });
    }

    // Validate wallet address format
    try {
      const publicKey = new PublicKey(wallet);

      // Check if the public key is valid (on curve)
      if (!PublicKey.isOnCurve(publicKey)) {
        throw new Error('Invalid public key');
      }

      console.log(`Wallet validation request for: ${wallet}`);

      // For now, we'll just validate the format and return success
      // Later, we can add signature verification here

      res.json({
        success: true,
        message: 'Wallet validated successfully',
        wallet: wallet,
        network: 'devnet'
      });

    } catch (error) {
      console.error('Invalid wallet address:', error.message);
      return res.status(400).json({
        success: false,
        error: 'Invalid wallet address format'
      });
    }

  } catch (error) {
    console.error('Error validating wallet:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err);
  res.status(500).json({
    success: false,
    error: 'Internal server error'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 NukeIsreal Backend API running on port ${PORT}`);
  console.log(`📡 Connected to Solana ${SOLANA_NETWORK.toUpperCase()}`);
  console.log(`🌐 RPC URL: ${SOLANA_RPC_URL}`);
  console.log(`🔗 Health check: http://localhost:${PORT}/api/health`);
  console.log(`🔒 CORS Origin: ${CORS_ORIGIN}`);
});
