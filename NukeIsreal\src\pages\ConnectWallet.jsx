import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

const ConnectWallet = () => {
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    // Check if wallet is already connected
    const savedWallet = localStorage.getItem('walletAddress');
    if (savedWallet) {
      // Verify the wallet is still connected to Phantom
      if (window.solana && window.solana.isPhantom && window.solana.isConnected) {
        navigate('/home');
      } else {
        // If Phantom is not connected but we have a saved wallet, clear it
        localStorage.removeItem('walletAddress');
      }
    }
  }, [navigate]);

  const connectPhantomWallet = async () => {
    try {
      setIsConnecting(true);
      setError('');

      // Check if Phantom wallet is installed
      if (!window.solana || !window.solana.isPhantom) {
        throw new Error('Phantom wallet is not installed. Please install it from phantom.app');
      }

      // Connect to Phantom wallet
      const response = await window.solana.connect();
      const publicKey = response.publicKey.toString();

      console.log('Connected to wallet:', publicKey);

      // Save wallet address to localStorage
      localStorage.setItem('walletAddress', publicKey);

      // Make POST request to backend for validation
      const validationResponse = await fetch('http://localhost:3001/api/validate-wallet', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          wallet: publicKey
        })
      });

      if (!validationResponse.ok) {
        throw new Error('Failed to validate wallet with backend');
      }

      const validationData = await validationResponse.json();
      
      if (validationData.success) {
        // Redirect to home page
        navigate('/home');
      } else {
        throw new Error('Wallet validation failed');
      }

    } catch (err) {
      console.error('Error connecting wallet:', err);
      setError(err.message);
      // Clear any saved wallet address on error
      localStorage.removeItem('walletAddress');
    } finally {
      setIsConnecting(false);
    }
  };

  return (
    <div style={{ 
      display: 'flex', 
      flexDirection: 'column', 
      alignItems: 'center', 
      justifyContent: 'center', 
      minHeight: '100vh',
      padding: '20px'
    }}>
      <h1>NukeIsreal Donation Platform</h1>
      <p>Connect your Phantom wallet to get started</p>
      
      {error && (
        <div style={{ 
          color: 'red', 
          marginBottom: '20px', 
          padding: '10px', 
          border: '1px solid red', 
          borderRadius: '5px',
          maxWidth: '400px'
        }}>
          {error}
        </div>
      )}

      <button 
        onClick={connectPhantomWallet}
        disabled={isConnecting}
        style={{
          padding: '15px 30px',
          fontSize: '16px',
          backgroundColor: '#512da8',
          color: 'white',
          border: 'none',
          borderRadius: '8px',
          cursor: isConnecting ? 'not-allowed' : 'pointer',
          opacity: isConnecting ? 0.7 : 1
        }}
      >
        {isConnecting ? 'Connecting...' : 'Connect Phantom Wallet'}
      </button>

      <p style={{ marginTop: '20px', fontSize: '14px', color: '#666' }}>
        Make sure you're connected to Solana Devnet
      </p>
    </div>
  );
};

export default ConnectWallet;
