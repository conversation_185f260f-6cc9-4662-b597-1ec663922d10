{"name": "nukeisreal-backend", "version": "1.0.0", "description": "Backend API for NukeIsreal donation platform", "main": "index.js", "type": "module", "scripts": {"start": "node index.js", "dev": "nodemon index.js"}, "keywords": ["solana", "donation", "web3"], "author": "", "license": "ISC", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "@solana/web3.js": "^1.87.6", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2"}}