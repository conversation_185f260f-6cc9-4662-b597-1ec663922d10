# 📱 Responsive Design Guide

## Overview
The NukeIsreal donation platform is fully responsive and optimized for all device sizes.

## Breakpoints
- **Mobile**: < 480px
- **Tablet**: 480px - 767px  
- **Desktop**: ≥ 768px

## Responsive Features

### 🔝 Top Bar
- **Mobile**: Compact padding, smaller logo, abbreviated text
- **Tablet**: Medium sizing with wrapped layout
- **Desktop**: Full spacing and text

### 🧭 Navigation
- **Mobile**: Horizontal scroll, icon-only labels, smaller buttons
- **Tablet**: Wrapped layout with medium buttons
- **Desktop**: Full horizontal layout

### 🎭 Main Content
- **Mobile**: Single column grid, reduced padding
- **Tablet**: 2-column grid with medium spacing
- **Desktop**: 3+ column grid with full spacing

### 💎 Donation Cards
- **Mobile**: 
  - Full width (100%)
  - Smaller images (160px height)
  - Stacked button layout
  - Reduced animations
- **Tablet**:
  - Auto-fit grid (280px min)
  - Medium images (180px height)
  - Side-by-side buttons
- **Desktop**:
  - Auto-fit grid (300px min)
  - Full images (200px height)
  - Enhanced hover effects

## Performance Optimizations

### Animation Adjustments
- **Mobile**: Reduced/disabled floating animations
- **Touch Devices**: Simplified hover effects
- **High DPI**: Enhanced shadows and effects

### Text Scaling
- **Mobile**: 13-16px base font sizes
- **Tablet**: 14-18px base font sizes  
- **Desktop**: 16-24px base font sizes

## Testing
Test responsive design by:
1. Resizing browser window
2. Using browser dev tools device emulation
3. Testing on actual mobile/tablet devices

## Custom Hook
Uses `useResponsive()` hook for:
- Real-time window size tracking
- Device type detection
- Optimized re-renders
