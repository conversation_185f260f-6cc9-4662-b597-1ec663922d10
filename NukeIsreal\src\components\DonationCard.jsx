import React, { useState } from 'react';
import useResponsive from '../hooks/useResponsive';

const DonationCard = ({ 
  title, 
  description, 
  imageUrl, 
  priceSOL, 
  onDonate,
  raised = 0,
  goal = 100 
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { isMobile, isTablet } = useResponsive();

  const handleDonate = async () => {
    setIsLoading(true);
    try {
      await onDonate();
    } catch (error) {
      console.error('Donation error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const progressPercentage = Math.min((raised / goal) * 100, 100);

  return (
    <div
      className="donation-card"
      style={{
        background: `
          linear-gradient(135deg,
            rgba(45, 74, 43, 0.9) 0%,
            rgba(74, 93, 35, 0.8) 25%,
            rgba(139, 115, 85, 0.7) 50%,
            rgba(101, 67, 33, 0.8) 75%,
            rgba(26, 26, 26, 0.9) 100%
          )
        `,
        borderRadius: isMobile || isTablet ? '8px' : '10px',
        padding: isMobile || isTablet ? '15px' : '20px',
        margin: isMobile || isTablet ? '10px' : '15px',
        minWidth: isMobile ? '280px' : '300px',
        maxWidth: isMobile || isTablet ? '100%' : '350px',
        width: isMobile ? '100%' : 'auto',
        border: `3px solid ${isHovered ? '#FFD700' : 'rgba(139, 115, 85, 0.6)'}`,
        boxShadow: isHovered
          ? '0 0 30px rgba(255, 215, 0, 0.4), inset 0 0 20px rgba(139, 0, 0, 0.2)'
          : '0 8px 25px rgba(0, 0, 0, 0.4), inset 0 2px 10px rgba(74, 93, 35, 0.3)',
        transform: isHovered ? 'translateY(-3px) scale(1.01)' : 'translateY(0) scale(1)',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        cursor: 'pointer',
        position: 'relative',
        overflow: 'hidden'
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Animated military camo pattern */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: `
          repeating-linear-gradient(
            45deg,
            rgba(74, 93, 35, 0.1),
            rgba(74, 93, 35, 0.1) 10px,
            rgba(139, 115, 85, 0.1) 10px,
            rgba(139, 115, 85, 0.1) 20px,
            rgba(101, 67, 33, 0.1) 20px,
            rgba(101, 67, 33, 0.1) 30px
          )
        `,
        backgroundSize: '60px 60px',
        animation: isHovered ? 'camoShift 4s ease infinite' : 'none',
        opacity: 0.3,
        zIndex: 0
      }} />

      {/* Content */}
      <div style={{ position: 'relative', zIndex: 1 }}>
        {/* Image */}
        <div style={{
          width: '100%',
          height: window.innerWidth < 768 ? '160px' : '200px',
          borderRadius: window.innerWidth < 768 ? '12px' : '15px',
          overflow: 'hidden',
          marginBottom: window.innerWidth < 768 ? '12px' : '15px',
          background: '#f0f0f0'
        }}>
          <img
            src={imageUrl || 'https://cdn-iladncp.nitrocdn.com/HqlEixensysOTCVgzDQPqXypWdYAZutW/assets/images/optimized/wp-content/uploads/2017/09/5a0807e4f1dacd8e73f5552bf617e60c.Qiam_NO-FINS.jpg'}
            alt={title}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
              transition: 'transform 0.3s ease',
              transform: isHovered ? 'scale(1.1)' : 'scale(1)'
            }}
            onError={(e) => {
              e.target.src = 'https://via.placeholder.com/300x200/FF6B6B/FFFFFF?text=🚀+MEME+POWER';
            }}
          />
        </div>

        {/* Title */}
        <h3 style={{
          color: '#FFD700',
          fontSize: window.innerWidth < 768 ? '18px' : '20px',
          fontWeight: 'bold',
          margin: '0 0 10px 0',
          textShadow: '0 2px 8px rgba(139, 0, 0, 0.8), 0 0 15px rgba(255, 215, 0, 0.3)',
          lineHeight: '1.2',
          letterSpacing: '0.5px',
          fontFamily: "'Courier New', monospace"
        }}>
          {title}
        </h3>

        {/* Description */}
        <p style={{
          color: 'rgba(255, 255, 255, 0.9)',
          fontSize: window.innerWidth < 768 ? '13px' : '14px',
          lineHeight: '1.5',
          margin: window.innerWidth < 768 ? '0 0 12px 0' : '0 0 15px 0',
          textShadow: '0 1px 3px rgba(0, 0, 0, 0.7)',
          fontFamily: "'Courier New', monospace"
        }}>
          {description}
        </p>

        {/* Progress Bar */}
        <div style={{
          background: 'rgba(0, 0, 0, 0.4)',
          borderRadius: '3px',
          height: '10px',
          marginBottom: '10px',
          overflow: 'hidden',
          border: '1px solid rgba(139, 115, 85, 0.5)'
        }}>
          <div style={{
            background: 'linear-gradient(90deg, #8B0000, #FFD700, #8B0000)',
            height: '100%',
            width: `${progressPercentage}%`,
            borderRadius: '2px',
            transition: 'width 0.5s ease',
            boxShadow: '0 0 10px rgba(255, 215, 0, 0.5)'
          }} />
        </div>

        {/* Progress Text */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          color: 'rgba(255, 255, 255, 0.8)',
          fontSize: window.innerWidth < 768 ? '11px' : '12px',
          marginBottom: window.innerWidth < 768 ? '12px' : '15px'
        }}>
          <span>{raised} SOL raised</span>
          <span>{goal} SOL goal</span>
        </div>

        {/* Price and Donate Button */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          flexWrap: window.innerWidth < 480 ? 'wrap' : 'nowrap',
          gap: window.innerWidth < 480 ? '10px' : '0'
        }}>
          <div style={{
            background: 'linear-gradient(45deg, rgba(139, 0, 0, 0.8), rgba(74, 93, 35, 0.8))',
            padding: window.innerWidth < 768 ? '6px 12px' : '8px 15px',
            borderRadius: '5px',
            border: '2px solid rgba(255, 215, 0, 0.6)',
            backdropFilter: 'blur(10px)',
            order: window.innerWidth < 480 ? 2 : 1
          }}>
            <span style={{
              color: '#FFD700',
              fontWeight: 'bold',
              fontSize: window.innerWidth < 768 ? '14px' : '16px',
              textShadow: '0 1px 3px rgba(0, 0, 0, 0.8)',
              fontFamily: "'Courier New', monospace"
            }}>
              🎖️ {priceSOL} SOL
            </span>
          </div>

          <button
            onClick={handleDonate}
            disabled={isLoading}
            style={{
              background: isLoading
                ? 'rgba(74, 93, 35, 0.5)'
                : 'linear-gradient(45deg, #8B0000, #FFD700, #8B0000)',
              color: 'white',
              border: '2px solid rgba(255, 215, 0, 0.8)',
              padding: window.innerWidth < 768 ? '10px 20px' : '12px 24px',
              borderRadius: '5px',
              fontWeight: 'bold',
              fontSize: window.innerWidth < 768 ? '12px' : '14px',
              cursor: isLoading ? 'not-allowed' : 'pointer',
              transition: 'all 0.3s ease',
              transform: isHovered && !isLoading ? 'scale(1.03)' : 'scale(1)',
              boxShadow: isHovered && !isLoading
                ? '0 0 20px rgba(255, 215, 0, 0.6), inset 0 2px 10px rgba(139, 0, 0, 0.3)'
                : '0 4px 15px rgba(0, 0, 0, 0.4)',
              order: window.innerWidth < 480 ? 1 : 2,
              width: window.innerWidth < 480 ? '100%' : 'auto',
              textShadow: '0 1px 3px rgba(0, 0, 0, 0.8)',
              fontFamily: "'Courier New', monospace",
              letterSpacing: '0.5px'
            }}
          >
            {isLoading ? '⚡ DEPLOYING...' : '⚔️ DEPLOY'}
          </button>
        </div>
      </div>

      <style jsx>{`
        @keyframes gradientShift {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }
      `}</style>
    </div>
  );
};

export default DonationCard;
