import React, { useState } from 'react';

const DonationCard = ({ 
  title, 
  description, 
  imageUrl, 
  priceSOL, 
  onDonate,
  raised = 0,
  goal = 100 
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleDonate = async () => {
    setIsLoading(true);
    try {
      await onDonate();
    } catch (error) {
      console.error('Donation error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const progressPercentage = Math.min((raised / goal) * 100, 100);

  return (
    <div 
      className="donation-card"
      style={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        borderRadius: '20px',
        padding: '20px',
        margin: '15px',
        minWidth: '300px',
        maxWidth: '350px',
        boxShadow: isHovered 
          ? '0 20px 40px rgba(102, 126, 234, 0.3)' 
          : '0 10px 20px rgba(0, 0, 0, 0.1)',
        transform: isHovered ? 'translateY(-5px) scale(1.02)' : 'translateY(0) scale(1)',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        cursor: 'pointer',
        position: 'relative',
        overflow: 'hidden'
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Animated background gradient */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: 'linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57)',
        backgroundSize: '400% 400%',
        animation: isHovered ? 'gradientShift 3s ease infinite' : 'none',
        opacity: 0.1,
        zIndex: 0
      }} />

      {/* Content */}
      <div style={{ position: 'relative', zIndex: 1 }}>
        {/* Image */}
        <div style={{
          width: '100%',
          height: '200px',
          borderRadius: '15px',
          overflow: 'hidden',
          marginBottom: '15px',
          background: '#f0f0f0'
        }}>
          <img 
            src={imageUrl || 'https://via.placeholder.com/300x200/FF6B6B/FFFFFF?text=🚀+MEME+POWER'}
            alt={title}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
              transition: 'transform 0.3s ease',
              transform: isHovered ? 'scale(1.1)' : 'scale(1)'
            }}
            onError={(e) => {
              e.target.src = 'https://via.placeholder.com/300x200/FF6B6B/FFFFFF?text=🚀+MEME+POWER';
            }}
          />
        </div>

        {/* Title */}
        <h3 style={{
          color: 'white',
          fontSize: '20px',
          fontWeight: 'bold',
          margin: '0 0 10px 0',
          textShadow: '0 2px 4px rgba(0,0,0,0.3)'
        }}>
          {title}
        </h3>

        {/* Description */}
        <p style={{
          color: 'rgba(255, 255, 255, 0.9)',
          fontSize: '14px',
          lineHeight: '1.5',
          margin: '0 0 15px 0'
        }}>
          {description}
        </p>

        {/* Progress Bar */}
        <div style={{
          background: 'rgba(255, 255, 255, 0.2)',
          borderRadius: '10px',
          height: '8px',
          marginBottom: '10px',
          overflow: 'hidden'
        }}>
          <div style={{
            background: 'linear-gradient(90deg, #00f5ff, #00d4aa)',
            height: '100%',
            width: `${progressPercentage}%`,
            borderRadius: '10px',
            transition: 'width 0.5s ease'
          }} />
        </div>

        {/* Progress Text */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          color: 'rgba(255, 255, 255, 0.8)',
          fontSize: '12px',
          marginBottom: '15px'
        }}>
          <span>{raised} SOL raised</span>
          <span>{goal} SOL goal</span>
        </div>

        {/* Price and Donate Button */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <div style={{
            background: 'rgba(255, 255, 255, 0.2)',
            padding: '8px 15px',
            borderRadius: '20px',
            backdropFilter: 'blur(10px)'
          }}>
            <span style={{
              color: 'white',
              fontWeight: 'bold',
              fontSize: '16px'
            }}>
              💎 {priceSOL} SOL
            </span>
          </div>

          <button
            onClick={handleDonate}
            disabled={isLoading}
            style={{
              background: isLoading 
                ? 'rgba(255, 255, 255, 0.3)' 
                : 'linear-gradient(45deg, #ff6b6b, #4ecdc4)',
              color: 'white',
              border: 'none',
              padding: '12px 24px',
              borderRadius: '25px',
              fontWeight: 'bold',
              fontSize: '14px',
              cursor: isLoading ? 'not-allowed' : 'pointer',
              transition: 'all 0.3s ease',
              transform: isHovered && !isLoading ? 'scale(1.05)' : 'scale(1)',
              boxShadow: '0 4px 15px rgba(0, 0, 0, 0.2)'
            }}
          >
            {isLoading ? '🔄 Sending...' : '🚀 Support'}
          </button>
        </div>
      </div>

      <style jsx>{`
        @keyframes gradientShift {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }
      `}</style>
    </div>
  );
};

export default DonationCard;
