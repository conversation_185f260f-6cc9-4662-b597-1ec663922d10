import React, { useState } from 'react';
import useResponsive from '../hooks/useResponsive';

const DonationCard = ({ 
  title, 
  description, 
  imageUrl, 
  priceSOL, 
  onDonate,
  raised = 0,
  goal = 100 
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { isMobile, isTablet } = useResponsive();

  const handleDonate = async () => {
    setIsLoading(true);
    try {
      await onDonate();
    } catch (error) {
      console.error('Donation error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const progressPercentage = Math.min((raised / goal) * 100, 100);

  return (
    <div
      className="donation-card"
      style={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        borderRadius: isMobile || isTablet ? '15px' : '20px',
        padding: isMobile || isTablet ? '15px' : '20px',
        margin: isMobile || isTablet ? '10px' : '15px',
        minWidth: isMobile ? '280px' : '300px',
        maxWidth: isMobile || isTablet ? '100%' : '350px',
        width: isMobile ? '100%' : 'auto',
        boxShadow: isHovered
          ? '0 20px 40px rgba(102, 126, 234, 0.3)'
          : '0 10px 20px rgba(0, 0, 0, 0.1)',
        transform: isHovered ? 'translateY(-5px) scale(1.02)' : 'translateY(0) scale(1)',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        cursor: 'pointer',
        position: 'relative',
        overflow: 'hidden'
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Animated background gradient */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: 'linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57)',
        backgroundSize: '400% 400%',
        animation: isHovered ? 'gradientShift 3s ease infinite' : 'none',
        opacity: 0.1,
        zIndex: 0
      }} />

      {/* Content */}
      <div style={{ position: 'relative', zIndex: 1 }}>
        {/* Image */}
        <div style={{
          width: '100%',
          height: window.innerWidth < 768 ? '160px' : '200px',
          borderRadius: window.innerWidth < 768 ? '12px' : '15px',
          overflow: 'hidden',
          marginBottom: window.innerWidth < 768 ? '12px' : '15px',
          background: '#f0f0f0'
        }}>
          <img
            src={imageUrl || 'https://cdn-iladncp.nitrocdn.com/HqlEixensysOTCVgzDQPqXypWdYAZutW/assets/images/optimized/wp-content/uploads/2017/09/5a0807e4f1dacd8e73f5552bf617e60c.Qiam_NO-FINS.jpg'}
            alt={title}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
              transition: 'transform 0.3s ease',
              transform: isHovered ? 'scale(1.1)' : 'scale(1)'
            }}
            onError={(e) => {
              e.target.src = 'https://via.placeholder.com/300x200/FF6B6B/FFFFFF?text=🚀+MEME+POWER';
            }}
          />
        </div>

        {/* Title */}
        <h3 style={{
          color: 'white',
          fontSize: window.innerWidth < 768 ? '18px' : '20px',
          fontWeight: 'bold',
          margin: '0 0 10px 0',
          textShadow: '0 2px 4px rgba(0,0,0,0.3)',
          lineHeight: '1.2'
        }}>
          {title}
        </h3>

        {/* Description */}
        <p style={{
          color: 'rgba(255, 255, 255, 0.9)',
          fontSize: window.innerWidth < 768 ? '13px' : '14px',
          lineHeight: '1.5',
          margin: window.innerWidth < 768 ? '0 0 12px 0' : '0 0 15px 0'
        }}>
          {description}
        </p>

        {/* Progress Bar */}
        <div style={{
          background: 'rgba(255, 255, 255, 0.2)',
          borderRadius: '10px',
          height: '8px',
          marginBottom: '10px',
          overflow: 'hidden'
        }}>
          <div style={{
            background: 'linear-gradient(90deg, #00f5ff, #00d4aa)',
            height: '100%',
            width: `${progressPercentage}%`,
            borderRadius: '10px',
            transition: 'width 0.5s ease'
          }} />
        </div>

        {/* Progress Text */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          color: 'rgba(255, 255, 255, 0.8)',
          fontSize: window.innerWidth < 768 ? '11px' : '12px',
          marginBottom: window.innerWidth < 768 ? '12px' : '15px'
        }}>
          <span>{raised} SOL raised</span>
          <span>{goal} SOL goal</span>
        </div>

        {/* Price and Donate Button */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          flexWrap: window.innerWidth < 480 ? 'wrap' : 'nowrap',
          gap: window.innerWidth < 480 ? '10px' : '0'
        }}>
          <div style={{
            background: 'rgba(255, 255, 255, 0.2)',
            padding: window.innerWidth < 768 ? '6px 12px' : '8px 15px',
            borderRadius: '20px',
            backdropFilter: 'blur(10px)',
            order: window.innerWidth < 480 ? 2 : 1
          }}>
            <span style={{
              color: 'white',
              fontWeight: 'bold',
              fontSize: window.innerWidth < 768 ? '14px' : '16px'
            }}>
              💎 {priceSOL} SOL
            </span>
          </div>

          <button
            onClick={handleDonate}
            disabled={isLoading}
            style={{
              background: isLoading
                ? 'rgba(255, 255, 255, 0.3)'
                : 'linear-gradient(45deg, #ff6b6b, #4ecdc4)',
              color: 'white',
              border: 'none',
              padding: window.innerWidth < 768 ? '10px 20px' : '12px 24px',
              borderRadius: '25px',
              fontWeight: 'bold',
              fontSize: window.innerWidth < 768 ? '12px' : '14px',
              cursor: isLoading ? 'not-allowed' : 'pointer',
              transition: 'all 0.3s ease',
              transform: isHovered && !isLoading ? 'scale(1.05)' : 'scale(1)',
              boxShadow: '0 4px 15px rgba(0, 0, 0, 0.2)',
              order: window.innerWidth < 480 ? 1 : 2,
              width: window.innerWidth < 480 ? '100%' : 'auto'
            }}
          >
            {isLoading ? '🔄 Sending...' : '🚀 Support'}
          </button>
        </div>
      </div>

      <style jsx>{`
        @keyframes gradientShift {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }
      `}</style>
    </div>
  );
};

export default DonationCard;
