import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DonationCard from '../components/DonationCard';
import NotificationModal from '../components/NotificationModal';
import useResponsive from '../hooks/useResponsive';
import { sendDonation, getWalletBalance, formatSOL } from '../utils/phantomWallet';

const Home = () => {
  const [walletAddress, setWalletAddress] = useState('');
  const [isDisconnecting, setIsDisconnecting] = useState(false);
  const [activeTab, setActiveTab] = useState('home');
  const [notification, setNotification] = useState({
    isOpen: false,
    type: 'info',
    title: '',
    message: '',
    details: '',
    signature: ''
  });
  const navigate = useNavigate();
  const { isMobile, isTablet } = useResponsive();

  useEffect(() => {
    // Get wallet address from localStorage
    const savedWallet = localStorage.getItem('walletAddress');

    if (!savedWallet) {
      // If no wallet is saved, redirect to connect page
      navigate('/connect');
    } else {
      setWalletAddress(savedWallet);
    }
  }, [navigate]);

  // Military donation campaigns - in real app this would come from API
  const donationItems = [
    {
      id: 1,
      title: "Operation Desert Shield ⚔️",
      description: "Support frontline troops with essential tactical equipment and protective gear for desert operations.",
      imageUrl: "https://cdn-iladncp.nitrocdn.com/HqlEixensysOTCVgzDQPqXypWdYAZutW/assets/images/optimized/wp-content/uploads/2017/09/5a0807e4f1dacd8e73f5552bf617e60c.Qiam_NO-FINS.jpg",
      priceSOL: 0.05,
      raised: 12.5,
      goal: 50
    },
    {
      id: 2,
      title: "Armored Division Support 🛡️",
      description: "Fund critical maintenance and upgrades for armored vehicles defending strategic positions.",
      imageUrl: "https://via.placeholder.com/300x200/654321/FFFFFF?text=🛡️+ARMOR+DIV",
      priceSOL: 0.1,
      raised: 8.3,
      goal: 25
    },
    {
      id: 3,
      title: "Air Strike Command 🚁",
      description: "Enable precision air support operations with advanced targeting systems and fuel supplies.",
      imageUrl: "https://via.placeholder.com/300x200/2D4A2B/FFFFFF?text=🚁+AIR+STRIKE",
      priceSOL: 0.25,
      raised: 45.7,
      goal: 100
    },
    {
      id: 4,
      title: "Special Forces Unit 🎯",
      description: "Equip elite special operations teams with cutting-edge stealth and reconnaissance gear.",
      imageUrl: "https://via.placeholder.com/300x200/8B0000/FFFFFF?text=🎯+SPEC+OPS",
      priceSOL: 0.08,
      raised: 22.1,
      goal: 40
    },
    {
      id: 5,
      title: "Combat Medic Corps 🏥",
      description: "Provide life-saving medical supplies and equipment for battlefield medics and field hospitals.",
      imageUrl: "https://via.placeholder.com/300x200/4A4A4A/FFFFFF?text=🏥+MEDIC+CORPS",
      priceSOL: 0.03,
      raised: 5.2,
      goal: 15
    },
    {
      id: 6,
      title: "Intelligence Network 📡",
      description: "Fund advanced surveillance and communication systems for strategic intelligence gathering.",
      imageUrl: "https://via.placeholder.com/300x200/8B7355/FFFFFF?text=📡+INTEL+NET",
      priceSOL: 0.15,
      raised: 33.8,
      goal: 75
    }
  ];

  const showNotification = (type, title, message, details = '', signature = '') => {
    setNotification({
      isOpen: true,
      type,
      title,
      message,
      details,
      signature
    });
  };

  const closeNotification = () => {
    setNotification(prev => ({ ...prev, isOpen: false }));
  };

  const handleDonate = async (item) => {
    try {
      console.log('Starting donation to:', item.title);

      // Show confirmation dialog
      const confirmed = window.confirm(
        `🚀 Donate ${item.priceSOL} SOL to ${item.title}?\n\n` +
        `Recipient: ${import.meta.env.VITE_DONATION_WALLET}\n` +
        `Network: ${import.meta.env.VITE_SOLANA_NETWORK?.toUpperCase() || 'DEVNET'}\n\n` +
        `Click OK to proceed with the transaction.`
      );

      if (!confirmed) {
        return;
      }

      // Send the donation
      const result = await sendDonation(item.priceSOL);

      // Show success notification
      showNotification(
        'success',
        'Donation Successful!',
        `Thank you for supporting ${item.title}! Your contribution helps build the meme revolution! 🎭✨`,
        `Amount: ${formatSOL(result.amount)}\nRecipient: ${result.recipient}\nSender: ${result.sender}`,
        result.signature
      );

      console.log('Donation successful:', result);

    } catch (error) {
      console.error('Donation error:', error);

      // Show error notification
      showNotification(
        'error',
        'Donation Failed',
        'Your donation could not be processed. Please check your wallet connection and try again.',
        `Error: ${error.message}\nProject: ${item.title}\nAmount: ${item.priceSOL} SOL`
      );
    }
  };

  const disconnectWallet = async () => {
    try {
      setIsDisconnecting(true);

      // Clear wallet from localStorage first
      localStorage.removeItem('walletAddress');

      // Disconnect from Phantom if available
      if (window.solana && window.solana.isPhantom) {
        await window.solana.disconnect();
        console.log('Disconnected from Phantom wallet');
      }

      // Clear the wallet address state
      setWalletAddress('');

      // Small delay to show the disconnecting state
      await new Promise(resolve => setTimeout(resolve, 500));

      // Redirect to connect page
      navigate('/connect');

    } catch (error) {
      console.error('Error disconnecting wallet:', error);
      // Even if disconnect fails, clear local state and redirect
      setWalletAddress('');
      navigate('/connect');
    } finally {
      setIsDisconnecting(false);
    }
  };

  const formatWalletAddress = (address) => {
    if (!address) return '';
    // Show first 4 and last 4 characters with ... in between
    return `${address.slice(0, 4)}...${address.slice(-4)}`;
  };

  const navigationItems = [
    { id: 'home', label: '🏠 Command Center', active: true },
    { id: 'about', label: '📋 Mission Brief', active: false },
    { id: 'leaderboard', label: '🎖️ Honor Roll', active: false },
    { id: 'history', label: '📊 Operations Log', active: false }
  ];

  return (
    <div style={{
      minHeight: '100vh',
      background: `
        linear-gradient(135deg,
          rgba(45, 74, 43, 0.9) 0%,
          rgba(74, 93, 35, 0.8) 25%,
          rgba(139, 115, 85, 0.7) 50%,
          rgba(101, 67, 33, 0.8) 75%,
          rgba(26, 26, 26, 0.9) 100%
        ),
        repeating-linear-gradient(
          45deg,
          transparent,
          transparent 10px,
          rgba(74, 93, 35, 0.1) 10px,
          rgba(74, 93, 35, 0.1) 20px
        )
      `,
      fontFamily: "'Courier New', 'Consolas', 'Monaco', monospace"
    }}>
      {/* Top Bar */}
      <div style={{
        position: 'sticky',
        top: 0,
        zIndex: 100,
        background: 'linear-gradient(90deg, rgba(45, 74, 43, 0.95), rgba(74, 93, 35, 0.95))',
        backdropFilter: 'blur(20px)',
        borderBottom: '3px solid rgba(255, 215, 0, 0.3)',
        borderTop: '2px solid rgba(139, 0, 0, 0.5)',
        padding: isMobile || isTablet ? '10px 15px' : '15px 30px',
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.5)'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          maxWidth: '1200px',
          margin: '0 auto',
          flexWrap: isMobile || isTablet ? 'wrap' : 'nowrap',
          gap: '10px'
        }}>
          {/* Logo/Brand */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: window.innerWidth < 768 ? '6px' : '10px',
            minWidth: 'fit-content'
          }}>
            <span style={{
              fontSize: window.innerWidth < 768 ? '20px' : '24px'
            }}>⚔️</span>
            <h1 style={{
              color: '#FFD700',
              fontSize: isMobile ? '16px' : isTablet ? '18px' : '24px',
              fontWeight: 'bold',
              margin: 0,
              textShadow: '0 2px 8px rgba(139, 0, 0, 0.8), 0 0 20px rgba(255, 215, 0, 0.3)',
              whiteSpace: 'nowrap',
              letterSpacing: '1px'
            }}>
              {window.innerWidth < 480 ? 'WAR FUND' : 'NukeIsreal WAR FUND'}
            </h1>
          </div>

          {/* Wallet Info */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: window.innerWidth < 768 ? '8px' : '15px',
            flexWrap: window.innerWidth < 480 ? 'wrap' : 'nowrap',
            justifyContent: window.innerWidth < 480 ? 'center' : 'flex-end'
          }}>
            <div style={{
              background: 'linear-gradient(45deg, rgba(139, 0, 0, 0.8), rgba(74, 93, 35, 0.8))',
              padding: window.innerWidth < 768 ? '6px 12px' : '8px 16px',
              borderRadius: '5px',
              border: '2px solid rgba(255, 215, 0, 0.6)',
              backdropFilter: 'blur(10px)',
              minWidth: 'fit-content'
            }}>
              <span style={{
                color: '#FFD700',
                fontSize: window.innerWidth < 768 ? '12px' : '14px',
                fontWeight: 'bold',
                textShadow: '0 1px 3px rgba(0, 0, 0, 0.8)',
                fontFamily: "'Courier New', monospace"
              }}>
                🎖️ {formatWalletAddress(walletAddress)}
              </span>
            </div>

            <button
              onClick={disconnectWallet}
              disabled={isDisconnecting}
              style={{
                background: 'linear-gradient(45deg, #8B0000, #654321)',
                color: '#FFD700',
                border: '2px solid rgba(255, 215, 0, 0.6)',
                padding: window.innerWidth < 768 ? '6px 12px' : '8px 16px',
                borderRadius: '5px',
                fontSize: window.innerWidth < 768 ? '10px' : '12px',
                cursor: isDisconnecting ? 'not-allowed' : 'pointer',
                opacity: isDisconnecting ? 0.7 : 1,
                transition: 'all 0.3s ease',
                whiteSpace: 'nowrap',
                fontWeight: 'bold',
                textShadow: '0 1px 3px rgba(0, 0, 0, 0.8)',
                fontFamily: "'Courier New', monospace"
              }}
            >
              {window.innerWidth < 480
                ? (isDisconnecting ? '⚡' : '🚪')
                : `${isDisconnecting ? '⚡' : '🚪'} ${isDisconnecting ? 'RETREATING...' : 'RETREAT'}`
              }
            </button>
          </div>
        </div>
      </div>

      {/* Navigation Bar */}
      <div style={{
        background: 'linear-gradient(90deg, rgba(74, 93, 35, 0.3), rgba(139, 115, 85, 0.3))',
        padding: window.innerWidth < 768 ? '15px 15px' : '20px 30px',
        borderBottom: '2px solid rgba(255, 215, 0, 0.2)',
        borderTop: '1px solid rgba(139, 0, 0, 0.3)',
        overflowX: window.innerWidth < 768 ? 'auto' : 'visible',
        boxShadow: 'inset 0 2px 10px rgba(0, 0, 0, 0.3)'
      }}>
        <div style={{
          maxWidth: '1200px',
          margin: '0 auto'
        }}>
          <div style={{
            display: 'flex',
            gap: window.innerWidth < 768 ? '10px' : '20px',
            flexWrap: window.innerWidth < 480 ? 'nowrap' : 'wrap',
            justifyContent: window.innerWidth < 768 ? 'flex-start' : 'center',
            minWidth: window.innerWidth < 480 ? 'max-content' : 'auto'
          }}>
            {navigationItems.map((item) => (
              <button
                key={item.id}
                onClick={() => setActiveTab(item.id)}
                style={{
                  background: activeTab === item.id
                    ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.3), rgba(139, 0, 0, 0.3))'
                    : 'rgba(74, 93, 35, 0.2)',
                  color: activeTab === item.id ? '#FFD700' : '#E0E0E0',
                  border: `2px solid ${activeTab === item.id ? '#FFD700' : 'rgba(139, 115, 85, 0.5)'}`,
                  padding: window.innerWidth < 768 ? '8px 16px' : '10px 20px',
                  borderRadius: '5px',
                  fontSize: window.innerWidth < 768 ? '12px' : '14px',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  backdropFilter: 'blur(10px)',
                  whiteSpace: 'nowrap',
                  minWidth: 'fit-content',
                  fontWeight: 'bold',
                  textShadow: '0 1px 3px rgba(0, 0, 0, 0.7)'
                }}
                onMouseEnter={(e) => {
                  if (activeTab !== item.id) {
                    e.target.style.background = 'rgba(255, 255, 255, 0.1)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (activeTab !== item.id) {
                    e.target.style.background = 'transparent';
                  }
                }}
              >
                {window.innerWidth < 480 ? item.label.split(' ')[0] : item.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div style={{
        padding: window.innerWidth < 768 ? '20px 15px' : '40px 30px',
        maxWidth: '1200px',
        margin: '0 auto'
      }}>
        {activeTab === 'home' && (
          <>
            {/* Welcome Section */}
            <div style={{
              textAlign: 'center',
              marginBottom: window.innerWidth < 768 ? '30px' : '40px'
            }}>
              <h2 style={{
                color: '#FFD700',
                fontSize: window.innerWidth < 480 ? '24px' : window.innerWidth < 768 ? '28px' : '36px',
                fontWeight: 'bold',
                margin: '0 0 15px 0',
                textShadow: '0 2px 8px rgba(139, 0, 0, 0.8), 0 0 30px rgba(255, 215, 0, 0.4)',
                lineHeight: '1.2',
                letterSpacing: '2px'
              }}>
                ⚔️ OPERATION FREEDOM FUND ⚔️
              </h2>
              <p style={{
                color: 'rgba(255, 255, 255, 0.9)',
                fontSize: window.innerWidth < 768 ? '16px' : '18px',
                maxWidth: window.innerWidth < 768 ? '100%' : '600px',
                margin: '0 auto',
                lineHeight: '1.6',
                padding: window.innerWidth < 768 ? '0 10px' : '0',
                textShadow: '0 1px 3px rgba(0, 0, 0, 0.7)'
              }}>
                Support critical military operations and defend strategic positions!
                Every donation strengthens our forces and brings victory closer. 🎖️
              </p>
            </div>

            {/* Donation Cards Grid */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: window.innerWidth < 480
                ? '1fr'
                : window.innerWidth < 768
                  ? 'repeat(auto-fit, minmax(280px, 1fr))'
                  : 'repeat(auto-fit, minmax(300px, 1fr))',
              gap: window.innerWidth < 768 ? '15px' : '20px',
              justifyItems: 'center'
            }}>
              {donationItems.map((item) => (
                <DonationCard
                  key={item.id}
                  title={item.title}
                  description={item.description}
                  imageUrl={item.imageUrl}
                  priceSOL={item.priceSOL}
                  raised={item.raised}
                  goal={item.goal}
                  onDonate={() => handleDonate(item)}
                />
              ))}
            </div>
          </>
        )}

        {/* Other Tab Content Placeholders */}
        {activeTab === 'about' && (
          <div style={{
            background: 'linear-gradient(135deg, rgba(45, 74, 43, 0.8), rgba(74, 93, 35, 0.6))',
            padding: window.innerWidth < 768 ? '20px' : '40px',
            borderRadius: '10px',
            textAlign: 'center',
            border: '3px solid rgba(255, 215, 0, 0.3)',
            boxShadow: '0 0 30px rgba(139, 0, 0, 0.3)'
          }}>
            <h2 style={{
              color: '#FFD700',
              marginBottom: '20px',
              fontSize: window.innerWidth < 768 ? '20px' : '24px',
              textShadow: '0 2px 8px rgba(139, 0, 0, 0.8)',
              fontFamily: "'Courier New', monospace",
              letterSpacing: '1px'
            }}>📋 MISSION BRIEFING</h2>
            <p style={{
              color: 'rgba(255, 255, 255, 0.9)',
              fontSize: window.innerWidth < 768 ? '14px' : '16px',
              lineHeight: '1.6',
              maxWidth: '100%',
              textShadow: '0 1px 3px rgba(0, 0, 0, 0.7)',
              fontFamily: "'Courier New', monospace"
            }}>
              NukeIsreal War Fund is a strategic military funding platform designed to support critical defense operations.
              Our mission is to provide essential resources for frontline troops, advanced equipment procurement,
              and tactical operations that ensure victory. Every contribution strengthens our forces and brings us closer to total victory! ⚔️🎖️
            </p>
          </div>
        )}

        {activeTab === 'leaderboard' && (
          <div style={{
            background: 'linear-gradient(135deg, rgba(139, 0, 0, 0.8), rgba(74, 93, 35, 0.6))',
            padding: window.innerWidth < 768 ? '20px' : '40px',
            borderRadius: '10px',
            textAlign: 'center',
            border: '3px solid rgba(255, 215, 0, 0.3)',
            boxShadow: '0 0 30px rgba(255, 215, 0, 0.3)'
          }}>
            <h2 style={{
              color: '#FFD700',
              marginBottom: '20px',
              fontSize: window.innerWidth < 768 ? '20px' : '24px',
              textShadow: '0 2px 8px rgba(139, 0, 0, 0.8)',
              fontFamily: "'Courier New', monospace",
              letterSpacing: '1px'
            }}>🎖️ HONOR ROLL</h2>
            <p style={{
              color: 'rgba(255, 255, 255, 0.9)',
              fontSize: window.innerWidth < 768 ? '14px' : '16px',
              textShadow: '0 1px 3px rgba(0, 0, 0, 0.7)',
              fontFamily: "'Courier New', monospace"
            }}>
              CLASSIFIED: Top contributors to the war effort will be recognized here! 🏅
            </p>
          </div>
        )}

        {activeTab === 'history' && (
          <div style={{
            background: 'linear-gradient(135deg, rgba(74, 93, 35, 0.8), rgba(139, 115, 85, 0.6))',
            padding: window.innerWidth < 768 ? '20px' : '40px',
            borderRadius: '10px',
            textAlign: 'center',
            border: '3px solid rgba(255, 215, 0, 0.3)',
            boxShadow: '0 0 30px rgba(74, 93, 35, 0.3)'
          }}>
            <h2 style={{
              color: '#FFD700',
              marginBottom: '20px',
              fontSize: window.innerWidth < 768 ? '20px' : '24px',
              textShadow: '0 2px 8px rgba(139, 0, 0, 0.8)',
              fontFamily: "'Courier New', monospace",
              letterSpacing: '1px'
            }}>📊 OPERATIONS LOG</h2>
            <p style={{
              color: 'rgba(255, 255, 255, 0.9)',
              fontSize: window.innerWidth < 768 ? '14px' : '16px',
              textShadow: '0 1px 3px rgba(0, 0, 0, 0.7)',
              fontFamily: "'Courier New', monospace"
            }}>
              Track your military contributions and deployment history! 📈⚔️
            </p>
          </div>
        )}
      </div>

      {/* Notification Modal */}
      <NotificationModal
        isOpen={notification.isOpen}
        onClose={closeNotification}
        type={notification.type}
        title={notification.title}
        message={notification.message}
        details={notification.details}
        signature={notification.signature}
      />
    </div>
  );
};

export default Home;
