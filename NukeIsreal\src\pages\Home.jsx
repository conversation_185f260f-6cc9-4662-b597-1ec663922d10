import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

const Home = () => {
  const [walletAddress, setWalletAddress] = useState('');
  const [isDisconnecting, setIsDisconnecting] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    // Get wallet address from localStorage
    const savedWallet = localStorage.getItem('walletAddress');
    
    if (!savedWallet) {
      // If no wallet is saved, redirect to connect page
      navigate('/connect');
    } else {
      setWalletAddress(savedWallet);
    }
  }, [navigate]);

  const disconnectWallet = async () => {
    try {
      setIsDisconnecting(true);

      // Clear wallet from localStorage first
      localStorage.removeItem('walletAddress');

      // Disconnect from Phantom if available
      if (window.solana && window.solana.isPhantom) {
        await window.solana.disconnect();
        console.log('Disconnected from Phantom wallet');
      }

      // Clear the wallet address state
      setWalletAddress('');

      // Small delay to show the disconnecting state
      await new Promise(resolve => setTimeout(resolve, 500));

      // Redirect to connect page
      navigate('/connect');

    } catch (error) {
      console.error('Error disconnecting wallet:', error);
      // Even if disconnect fails, clear local state and redirect
      setWalletAddress('');
      navigate('/connect');
    } finally {
      setIsDisconnecting(false);
    }
  };

  const formatWalletAddress = (address) => {
    if (!address) return '';
    // Show first 4 and last 4 characters with ... in between
    return `${address.slice(0, 4)}...${address.slice(-4)}`;
  };

  return (
    <div style={{ 
      display: 'flex', 
      flexDirection: 'column', 
      alignItems: 'center', 
      justifyContent: 'center', 
      minHeight: '100vh',
      padding: '20px'
    }}>
      <h1>Welcome to NukeIsreal Donation Platform</h1>
      
      <div style={{
        backgroundColor: '#f5f5f5',
        padding: '20px',
        borderRadius: '10px',
        marginBottom: '30px',
        textAlign: 'center'
      }}>
        <h2>Connected Wallet</h2>
        <p style={{ 
          fontSize: '18px', 
          fontFamily: 'monospace',
          color: '#333',
          wordBreak: 'break-all'
        }}>
          {walletAddress}
        </p>
        <p style={{ fontSize: '14px', color: '#666' }}>
          Short: {formatWalletAddress(walletAddress)}
        </p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <p style={{ fontSize: '16px', color: '#666' }}>
          🎉 Wallet successfully connected to Solana Devnet!
        </p>
        <p style={{ fontSize: '14px', color: '#888' }}>
          Donation functionality will be added in the next phase.
        </p>
      </div>

      <button
        onClick={disconnectWallet}
        disabled={isDisconnecting}
        style={{
          padding: '10px 20px',
          fontSize: '14px',
          backgroundColor: isDisconnecting ? '#6c757d' : '#dc3545',
          color: 'white',
          border: 'none',
          borderRadius: '5px',
          cursor: isDisconnecting ? 'not-allowed' : 'pointer',
          opacity: isDisconnecting ? 0.7 : 1
        }}
      >
        {isDisconnecting ? 'Disconnecting...' : 'Disconnect Wallet'}
      </button>
    </div>
  );
};

export default Home;
