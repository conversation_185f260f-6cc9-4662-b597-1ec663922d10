import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DonationCard from '../components/DonationCard';

const Home = () => {
  const [walletAddress, setWalletAddress] = useState('');
  const [isDisconnecting, setIsDisconnecting] = useState(false);
  const [activeTab, setActiveTab] = useState('home');
  const navigate = useNavigate();

  useEffect(() => {
    // Get wallet address from localStorage
    const savedWallet = localStorage.getItem('walletAddress');

    if (!savedWallet) {
      // If no wallet is saved, redirect to connect page
      navigate('/connect');
    } else {
      setWalletAddress(savedWallet);
    }
  }, [navigate]);

  // Sample donation data - in real app this would come from API
  const donationItems = [
    {
      id: 1,
      title: "Save the Frogs DAO 🐸",
      description: "Help our amphibian friends build the ultimate lily pad ecosystem on Solana!",
      imageUrl: "https://via.placeholder.com/300x200/4ECDC4/FFFFFF?text=🐸+FROG+DAO",
      priceSOL: 0.05,
      raised: 12.5,
      goal: 50
    },
    {
      id: 2,
      title: "Fix Pepe's Lambo 🚗",
      description: "Pepe's ride broke down! Let's get our meme king back on the road to the moon.",
      imageUrl: "https://via.placeholder.com/300x200/FF6B6B/FFFFFF?text=🚗+PEPE+LAMBO",
      priceSOL: 0.1,
      raised: 8.3,
      goal: 25
    },
    {
      id: 3,
      title: "Doge Space Mission 🚀",
      description: "Much wow! Help Doge reach the actual moon with this interstellar adventure.",
      imageUrl: "https://via.placeholder.com/300x200/FFD93D/FFFFFF?text=🚀+DOGE+MOON",
      priceSOL: 0.25,
      raised: 45.7,
      goal: 100
    },
    {
      id: 4,
      title: "Cat Meme Sanctuary 🐱",
      description: "A safe haven for all the internet cats. Grumpy Cat would be proud!",
      imageUrl: "https://via.placeholder.com/300x200/A8E6CF/FFFFFF?text=🐱+CAT+MEMES",
      priceSOL: 0.08,
      raised: 22.1,
      goal: 40
    },
    {
      id: 5,
      title: "Wojak Therapy Fund 💭",
      description: "Even memes need mental health support. Let's help Wojak feel better.",
      imageUrl: "https://via.placeholder.com/300x200/DDA0DD/FFFFFF?text=💭+WOJAK+HELP",
      priceSOL: 0.03,
      raised: 5.2,
      goal: 15
    },
    {
      id: 6,
      title: "Shiba Inu Park 🏞️",
      description: "Building the ultimate dog park for all the good boys and girls of crypto.",
      imageUrl: "https://via.placeholder.com/300x200/98FB98/FFFFFF?text=🏞️+SHIBA+PARK",
      priceSOL: 0.15,
      raised: 33.8,
      goal: 75
    }
  ];

  const handleDonate = async (item) => {
    console.log('Donating to:', item.title);
    // TODO: Implement Phantom wallet transaction
    alert(`Coming soon: Donate ${item.priceSOL} SOL to ${item.title}`);
  };

  const disconnectWallet = async () => {
    try {
      setIsDisconnecting(true);

      // Clear wallet from localStorage first
      localStorage.removeItem('walletAddress');

      // Disconnect from Phantom if available
      if (window.solana && window.solana.isPhantom) {
        await window.solana.disconnect();
        console.log('Disconnected from Phantom wallet');
      }

      // Clear the wallet address state
      setWalletAddress('');

      // Small delay to show the disconnecting state
      await new Promise(resolve => setTimeout(resolve, 500));

      // Redirect to connect page
      navigate('/connect');

    } catch (error) {
      console.error('Error disconnecting wallet:', error);
      // Even if disconnect fails, clear local state and redirect
      setWalletAddress('');
      navigate('/connect');
    } finally {
      setIsDisconnecting(false);
    }
  };

  const formatWalletAddress = (address) => {
    if (!address) return '';
    // Show first 4 and last 4 characters with ... in between
    return `${address.slice(0, 4)}...${address.slice(-4)}`;
  };

  const navigationItems = [
    { id: 'home', label: '🏠 Home', active: true },
    { id: 'about', label: '📖 About Project', active: false },
    { id: 'leaderboard', label: '🏆 Leaderboard', active: false },
    { id: 'history', label: '📊 Donate History', active: false }
  ];

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      fontFamily: 'Arial, sans-serif'
    }}>
      {/* Top Bar */}
      <div style={{
        position: 'sticky',
        top: 0,
        zIndex: 100,
        background: 'rgba(255, 255, 255, 0.1)',
        backdropFilter: 'blur(20px)',
        borderBottom: '1px solid rgba(255, 255, 255, 0.2)',
        padding: '15px 30px'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          maxWidth: '1200px',
          margin: '0 auto'
        }}>
          {/* Logo/Brand */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '10px'
          }}>
            <span style={{ fontSize: '24px' }}>🚀</span>
            <h1 style={{
              color: 'white',
              fontSize: '24px',
              fontWeight: 'bold',
              margin: 0,
              textShadow: '0 2px 4px rgba(0,0,0,0.3)'
            }}>
              Memes4Change
            </h1>
          </div>

          {/* Wallet Info */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '15px'
          }}>
            <div style={{
              background: 'rgba(255, 255, 255, 0.2)',
              padding: '8px 16px',
              borderRadius: '20px',
              backdropFilter: 'blur(10px)'
            }}>
              <span style={{
                color: 'white',
                fontSize: '14px',
                fontWeight: '500'
              }}>
                💎 {formatWalletAddress(walletAddress)}
              </span>
            </div>

            <button
              onClick={disconnectWallet}
              disabled={isDisconnecting}
              style={{
                background: 'rgba(220, 53, 69, 0.8)',
                color: 'white',
                border: 'none',
                padding: '8px 16px',
                borderRadius: '20px',
                fontSize: '12px',
                cursor: isDisconnecting ? 'not-allowed' : 'pointer',
                opacity: isDisconnecting ? 0.7 : 1,
                transition: 'all 0.3s ease'
              }}
            >
              {isDisconnecting ? '🔄' : '🚪'} {isDisconnecting ? 'Disconnecting...' : 'Disconnect'}
            </button>
          </div>
        </div>
      </div>

      {/* Navigation Bar */}
      <div style={{
        background: 'rgba(255, 255, 255, 0.05)',
        padding: '20px 30px',
        borderBottom: '1px solid rgba(255, 255, 255, 0.1)'
      }}>
        <div style={{
          maxWidth: '1200px',
          margin: '0 auto'
        }}>
          <div style={{
            display: 'flex',
            gap: '20px',
            flexWrap: 'wrap'
          }}>
            {navigationItems.map((item) => (
              <button
                key={item.id}
                onClick={() => setActiveTab(item.id)}
                style={{
                  background: activeTab === item.id
                    ? 'rgba(255, 255, 255, 0.2)'
                    : 'transparent',
                  color: 'white',
                  border: '1px solid rgba(255, 255, 255, 0.3)',
                  padding: '10px 20px',
                  borderRadius: '25px',
                  fontSize: '14px',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  backdropFilter: 'blur(10px)'
                }}
                onMouseEnter={(e) => {
                  if (activeTab !== item.id) {
                    e.target.style.background = 'rgba(255, 255, 255, 0.1)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (activeTab !== item.id) {
                    e.target.style.background = 'transparent';
                  }
                }}
              >
                {item.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div style={{
        padding: '40px 30px',
        maxWidth: '1200px',
        margin: '0 auto'
      }}>
        {activeTab === 'home' && (
          <>
            {/* Welcome Section */}
            <div style={{
              textAlign: 'center',
              marginBottom: '40px'
            }}>
              <h2 style={{
                color: 'white',
                fontSize: '36px',
                fontWeight: 'bold',
                margin: '0 0 15px 0',
                textShadow: '0 2px 4px rgba(0,0,0,0.3)'
              }}>
                🎭 Welcome to the Meme Revolution! 🎭
              </h2>
              <p style={{
                color: 'rgba(255, 255, 255, 0.9)',
                fontSize: '18px',
                maxWidth: '600px',
                margin: '0 auto',
                lineHeight: '1.6'
              }}>
                Support your favorite meme projects and help build the most epic crypto community!
                Every donation makes a difference in the memeverse. 🌟
              </p>
            </div>

            {/* Donation Cards Grid */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
              gap: '20px',
              justifyItems: 'center'
            }}>
              {donationItems.map((item) => (
                <DonationCard
                  key={item.id}
                  title={item.title}
                  description={item.description}
                  imageUrl={item.imageUrl}
                  priceSOL={item.priceSOL}
                  raised={item.raised}
                  goal={item.goal}
                  onDonate={() => handleDonate(item)}
                />
              ))}
            </div>
          </>
        )}

        {/* Other Tab Content Placeholders */}
        {activeTab === 'about' && (
          <div style={{
            background: 'rgba(255, 255, 255, 0.1)',
            padding: '40px',
            borderRadius: '20px',
            textAlign: 'center'
          }}>
            <h2 style={{ color: 'white', marginBottom: '20px' }}>📖 About Memes4Change</h2>
            <p style={{ color: 'rgba(255, 255, 255, 0.9)', fontSize: '16px', lineHeight: '1.6' }}>
              We're building the ultimate platform where memes meet meaningful change!
              Our mission is to harness the power of internet culture to fund amazing projects
              and bring communities together. From saving digital frogs to launching memes to the moon,
              every donation counts! 🚀✨
            </p>
          </div>
        )}

        {activeTab === 'leaderboard' && (
          <div style={{
            background: 'rgba(255, 255, 255, 0.1)',
            padding: '40px',
            borderRadius: '20px',
            textAlign: 'center'
          }}>
            <h2 style={{ color: 'white', marginBottom: '20px' }}>🏆 Top Donors</h2>
            <p style={{ color: 'rgba(255, 255, 255, 0.9)' }}>
              Coming soon! See who's leading the meme revolution! 🎖️
            </p>
          </div>
        )}

        {activeTab === 'history' && (
          <div style={{
            background: 'rgba(255, 255, 255, 0.1)',
            padding: '40px',
            borderRadius: '20px',
            textAlign: 'center'
          }}>
            <h2 style={{ color: 'white', marginBottom: '20px' }}>📊 Your Donation History</h2>
            <p style={{ color: 'rgba(255, 255, 255, 0.9)' }}>
              Track all your amazing contributions to the memeverse! 📈
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Home;
