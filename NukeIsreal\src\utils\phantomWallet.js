import { Connection, PublicKey, Transaction, SystemProgram, LAMPORTS_PER_SOL } from '@solana/web3.js';

// Get Solana connection
const getConnection = () => {
  const rpcUrl = import.meta.env.VITE_SOLANA_RPC_URL || 'https://api.devnet.solana.com';
  return new Connection(rpcUrl, 'confirmed');
};

// Get donation recipient wallet address
const getDonationWallet = () => {
  const walletAddress = import.meta.env.VITE_DONATION_WALLET;
  if (!walletAddress) {
    throw new Error('Donation wallet address not configured in environment variables');
  }
  return new PublicKey(walletAddress);
};

// Check if Phantom wallet is available
export const isPhantomAvailable = () => {
  return typeof window !== 'undefined' && window.solana && window.solana.isPhantom;
};

// Connect to Phantom wallet
export const connectPhantom = async () => {
  if (!isPhantomAvailable()) {
    throw new Error('Phantom wallet is not installed. Please install it from phantom.app');
  }

  try {
    const response = await window.solana.connect();
    return response.publicKey;
  } catch (error) {
    throw new Error(`Failed to connect to Phantom wallet: ${error.message}`);
  }
};

// Get wallet balance
export const getWalletBalance = async (publicKey) => {
  try {
    const connection = getConnection();
    const balance = await connection.getBalance(publicKey);
    return balance / LAMPORTS_PER_SOL; // Convert lamports to SOL
  } catch (error) {
    throw new Error(`Failed to get wallet balance: ${error.message}`);
  }
};

// Send SOL donation
export const sendDonation = async (amountSOL) => {
  if (!isPhantomAvailable()) {
    throw new Error('Phantom wallet is not installed');
  }

  if (!window.solana.isConnected) {
    throw new Error('Phantom wallet is not connected');
  }

  try {
    const connection = getConnection();
    const fromPubkey = window.solana.publicKey;
    const toPubkey = getDonationWallet();
    
    // Convert SOL to lamports
    const lamports = Math.round(amountSOL * LAMPORTS_PER_SOL);
    
    // Check if user has enough balance
    const balance = await getWalletBalance(fromPubkey);
    if (balance < amountSOL) {
      throw new Error(`Insufficient balance. You have ${balance.toFixed(4)} SOL, but need ${amountSOL} SOL`);
    }

    // Create transaction
    const transaction = new Transaction().add(
      SystemProgram.transfer({
        fromPubkey,
        toPubkey,
        lamports,
      })
    );

    // Get recent blockhash
    const { blockhash } = await connection.getLatestBlockhash();
    transaction.recentBlockhash = blockhash;
    transaction.feePayer = fromPubkey;

    // Sign and send transaction
    const signedTransaction = await window.solana.signTransaction(transaction);
    const signature = await connection.sendRawTransaction(signedTransaction.serialize());

    // Wait for confirmation
    const confirmation = await connection.confirmTransaction(signature, 'confirmed');
    
    if (confirmation.value.err) {
      throw new Error(`Transaction failed: ${confirmation.value.err}`);
    }

    return {
      signature,
      amount: amountSOL,
      recipient: toPubkey.toString(),
      sender: fromPubkey.toString()
    };

  } catch (error) {
    console.error('Donation transaction error:', error);
    throw new Error(`Donation failed: ${error.message}`);
  }
};

// Get transaction status
export const getTransactionStatus = async (signature) => {
  try {
    const connection = getConnection();
    const status = await connection.getSignatureStatus(signature);
    return status;
  } catch (error) {
    throw new Error(`Failed to get transaction status: ${error.message}`);
  }
};

// Format SOL amount for display
export const formatSOL = (amount) => {
  return `${amount.toFixed(4)} SOL`;
};

// Validate SOL amount
export const validateSOLAmount = (amount) => {
  const num = parseFloat(amount);
  if (isNaN(num) || num <= 0) {
    throw new Error('Please enter a valid SOL amount greater than 0');
  }
  if (num < 0.001) {
    throw new Error('Minimum donation amount is 0.001 SOL');
  }
  if (num > 1000) {
    throw new Error('Maximum donation amount is 1000 SOL');
  }
  return num;
};
