import { Connection, PublicKey, Transaction, SystemProgram, LAMPORTS_PER_SOL } from '@solana/web3.js';

// Get Solana connection
const getConnection = () => {
  const rpcUrl = import.meta.env.VITE_SOLANA_RPC_URL || 'https://api.devnet.solana.com';
  return new Connection(rpcUrl, 'confirmed');
};

// Get donation recipient wallet address
const getDonationWallet = () => {
  const walletAddress = import.meta.env.VITE_DONATION_WALLET;
  if (!walletAddress) {
    throw new Error('Donation wallet address not configured in environment variables');
  }
  return new PublicKey(walletAddress);
};

// Check if Phantom wallet is available
export const isPhantomAvailable = () => {
  return typeof window !== 'undefined' && window.solana && window.solana.isPhantom;
};

// Connect to Phantom wallet
export const connectPhantom = async () => {
  if (!isPhantomAvailable()) {
    throw new Error('Phantom wallet is not installed. Please install it from phantom.app');
  }

  try {
    const response = await window.solana.connect();
    return response.publicKey;
  } catch (error) {
    throw new Error(`Failed to connect to Phantom wallet: ${error.message}`);
  }
};

// Get wallet balance
export const getWalletBalance = async (publicKey) => {
  try {
    const connection = getConnection();
    const balance = await connection.getBalance(publicKey);
    return balance / LAMPORTS_PER_SOL; // Convert lamports to SOL
  } catch (error) {
    throw new Error(`Failed to get wallet balance: ${error.message}`);
  }
};

// Send SOL donation via backend API
export const sendDonation = async (amountSOL, operationId, operationTitle) => {
  if (!isPhantomAvailable()) {
    throw new Error('Phantom wallet is not installed');
  }

  if (!window.solana.isConnected) {
    throw new Error('Phantom wallet is not connected');
  }

  try {
    const senderPublicKey = window.solana.publicKey.toString();
    const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001';

    // Step 1: Request transaction from backend
    const prepareResponse = await fetch(`${apiUrl}/api/donate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        senderPublicKey,
        amount: amountSOL,
        operationId,
        operationTitle
      })
    });

    if (!prepareResponse.ok) {
      const errorData = await prepareResponse.json();
      throw new Error(errorData.error || 'Failed to prepare transaction');
    }

    const { data } = await prepareResponse.json();

    // Step 2: Deserialize and sign transaction
    const transactionBuffer = Buffer.from(data.transaction, 'base64');
    const transaction = Transaction.from(transactionBuffer);

    // Sign transaction with Phantom
    const signedTransaction = await window.solana.signTransaction(transaction);

    // Step 3: Send transaction to Solana network
    const connection = getConnection();
    const signature = await connection.sendRawTransaction(signedTransaction.serialize());

    // Step 4: Confirm with backend
    const confirmResponse = await fetch(`${apiUrl}/api/confirm-donation`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        signature,
        amount: amountSOL,
        operationId,
        operationTitle
      })
    });

    if (!confirmResponse.ok) {
      const errorData = await confirmResponse.json();
      throw new Error(errorData.error || 'Failed to confirm transaction');
    }

    const confirmData = await confirmResponse.json();

    return {
      signature,
      amount: amountSOL,
      recipient: data.recipient,
      sender: senderPublicKey,
      operation: operationTitle,
      explorerUrl: confirmData.data.explorerUrl
    };

  } catch (error) {
    console.error('Donation transaction error:', error);
    throw new Error(`Mission failed: ${error.message}`);
  }
};

// Get transaction status
export const getTransactionStatus = async (signature) => {
  try {
    const connection = getConnection();
    const status = await connection.getSignatureStatus(signature);
    return status;
  } catch (error) {
    throw new Error(`Failed to get transaction status: ${error.message}`);
  }
};

// Format SOL amount for display
export const formatSOL = (amount) => {
  return `${amount.toFixed(4)} SOL`;
};

// Validate SOL amount
export const validateSOLAmount = (amount) => {
  const num = parseFloat(amount);
  if (isNaN(num) || num <= 0) {
    throw new Error('Please enter a valid SOL amount greater than 0');
  }
  if (num < 0.001) {
    throw new Error('Minimum donation amount is 0.001 SOL');
  }
  if (num > 1000) {
    throw new Error('Maximum donation amount is 1000 SOL');
  }
  return num;
};
