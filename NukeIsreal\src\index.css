:root {
  font-family: 'Courier New', '<PERSON>sol<PERSON>', 'Monaco', monospace;
  line-height: 1.5;
  font-weight: 400;

  /* Military Color Palette */
  --military-dark-green: #2d4a2b;
  --military-olive: #4a5d23;
  --military-tan: #8b7355;
  --military-brown: #654321;
  --military-black: #1a1a1a;
  --military-gray: #4a4a4a;
  --military-red: #8b0000;
  --military-gold: #ffd700;

  color-scheme: dark;
  color: rgba(255, 255, 255, 0.9);
  background-color: var(--military-black);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

/* Military-inspired animations */
@keyframes camoShift {
  0% { background-position: 0% 50%; }
  25% { background-position: 25% 75%; }
  50% { background-position: 100% 50%; }
  75% { background-position: 75% 25%; }
  100% { background-position: 0% 50%; }
}

@keyframes march {
  0%, 100% { transform: translateY(0px) rotateX(0deg); }
  50% { transform: translateY(-5px) rotateX(2deg); }
}

@keyframes tactical {
  0%, 100% { transform: scale(1) rotateZ(0deg); }
  50% { transform: scale(1.02) rotateZ(0.5deg); }
}

@keyframes radar {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes alert {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes deploy {
  0% { transform: translateY(-20px) scale(0.8); opacity: 0; }
  50% { transform: translateY(-10px) scale(0.9); opacity: 0.5; }
  100% { transform: translateY(0px) scale(1); opacity: 1; }
}

/* Military card animations */
.donation-card {
  animation: march 4s ease-in-out infinite;
  border: 2px solid var(--military-olive);
  box-shadow: 0 0 20px rgba(139, 0, 0, 0.3);
}

.donation-card:nth-child(2n) {
  animation-delay: -1s;
}

.donation-card:nth-child(3n) {
  animation-delay: -2s;
}

.donation-card:hover {
  animation: tactical 0.5s ease-in-out;
  border-color: var(--military-gold);
  box-shadow: 0 0 30px rgba(255, 215, 0, 0.4);
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, var(--military-olive), var(--military-tan));
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, var(--military-dark-green), var(--military-gold));
}

/* Military button effects */
button {
  position: relative;
  overflow: hidden;
}

button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.3), transparent);
  transition: left 0.5s;
}

button:hover::before {
  left: 100%;
}

/* Military text effects */
.military-text {
  animation: alert 2s ease-in-out infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
  body {
    font-size: 14px;
  }

  .donation-card {
    animation-duration: 6s;
  }

  /* Reduce motion for mobile */
  .donation-card:hover {
    transform: translateY(-2px) scale(1.01) !important;
  }
}

@media (max-width: 480px) {
  body {
    font-size: 13px;
  }

  /* Disable floating animation on very small screens */
  .donation-card {
    animation: none;
  }

  /* Simplify hover effects */
  .donation-card:hover {
    transform: translateY(-1px) !important;
  }

  /* Ensure text is readable */
  h1, h2, h3 {
    line-height: 1.3 !important;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .donation-card:hover {
    transform: none !important;
  }

  button:hover {
    transform: none !important;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .donation-card {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
  }
}

/* Landscape mobile orientation */
@media (max-width: 768px) and (orientation: landscape) {
  .donation-card {
    max-width: 280px;
  }
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  color: white;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
    color: #213547;
  }
}
