import React from 'react';

const NotificationModal = ({ 
  isOpen, 
  onClose, 
  type = 'info', // 'success', 'error', 'warning', 'info'
  title, 
  message, 
  details,
  signature 
}) => {
  if (!isOpen) return null;

  const getTypeStyles = () => {
    switch (type) {
      case 'success':
        return {
          background: 'linear-gradient(135deg, #4CAF50, #45a049)',
          icon: '🎉'
        };
      case 'error':
        return {
          background: 'linear-gradient(135deg, #f44336, #d32f2f)',
          icon: '❌'
        };
      case 'warning':
        return {
          background: 'linear-gradient(135deg, #ff9800, #f57c00)',
          icon: '⚠️'
        };
      default:
        return {
          background: 'linear-gradient(135deg, #2196F3, #1976D2)',
          icon: 'ℹ️'
        };
    }
  };

  const typeStyles = getTypeStyles();

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    alert('Copied to clipboard!');
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000,
      padding: '20px'
    }}>
      <div style={{
        background: typeStyles.background,
        borderRadius: '20px',
        padding: '30px',
        maxWidth: '500px',
        width: '100%',
        boxShadow: '0 20px 40px rgba(0, 0, 0, 0.3)',
        position: 'relative',
        color: 'white'
      }}>
        {/* Close button */}
        <button
          onClick={onClose}
          style={{
            position: 'absolute',
            top: '15px',
            right: '15px',
            background: 'rgba(255, 255, 255, 0.2)',
            border: 'none',
            borderRadius: '50%',
            width: '30px',
            height: '30px',
            color: 'white',
            cursor: 'pointer',
            fontSize: '16px'
          }}
        >
          ×
        </button>

        {/* Icon and Title */}
        <div style={{
          textAlign: 'center',
          marginBottom: '20px'
        }}>
          <div style={{ fontSize: '48px', marginBottom: '10px' }}>
            {typeStyles.icon}
          </div>
          <h2 style={{
            margin: 0,
            fontSize: '24px',
            fontWeight: 'bold'
          }}>
            {title}
          </h2>
        </div>

        {/* Message */}
        <div style={{
          textAlign: 'center',
          marginBottom: '20px'
        }}>
          <p style={{
            fontSize: '16px',
            lineHeight: '1.5',
            margin: 0,
            opacity: 0.9
          }}>
            {message}
          </p>
        </div>

        {/* Details */}
        {details && (
          <div style={{
            background: 'rgba(255, 255, 255, 0.1)',
            borderRadius: '10px',
            padding: '15px',
            marginBottom: '20px',
            fontSize: '14px'
          }}>
            {details.split('\n').map((line, index) => (
              <div key={index} style={{ marginBottom: '5px' }}>
                {line}
              </div>
            ))}
          </div>
        )}

        {/* Transaction Signature */}
        {signature && (
          <div style={{
            background: 'rgba(255, 255, 255, 0.1)',
            borderRadius: '10px',
            padding: '15px',
            marginBottom: '20px'
          }}>
            <div style={{ 
              fontSize: '12px', 
              opacity: 0.8, 
              marginBottom: '5px' 
            }}>
              Transaction Signature:
            </div>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '10px'
            }}>
              <code style={{
                background: 'rgba(0, 0, 0, 0.2)',
                padding: '5px 8px',
                borderRadius: '5px',
                fontSize: '12px',
                flex: 1,
                wordBreak: 'break-all'
              }}>
                {signature}
              </code>
              <button
                onClick={() => copyToClipboard(signature)}
                style={{
                  background: 'rgba(255, 255, 255, 0.2)',
                  border: 'none',
                  borderRadius: '5px',
                  padding: '5px 10px',
                  color: 'white',
                  cursor: 'pointer',
                  fontSize: '12px'
                }}
              >
                📋 Copy
              </button>
            </div>
          </div>
        )}

        {/* Close Button */}
        <div style={{ textAlign: 'center' }}>
          <button
            onClick={onClose}
            style={{
              background: 'rgba(255, 255, 255, 0.2)',
              border: 'none',
              borderRadius: '25px',
              padding: '12px 30px',
              color: 'white',
              cursor: 'pointer',
              fontSize: '16px',
              fontWeight: 'bold'
            }}
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default NotificationModal;
